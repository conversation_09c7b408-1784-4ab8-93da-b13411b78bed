"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "~/components/ui/button"
import { Input } from "~/components/ui/input"
import { ScrollArea } from "~/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "~/components/ui/avatar"
import { Send, Plus, User, Bo<PERSON>, <PERSON>u, Sun, Moon } from "lucide-react"
import { useTheme } from "~/components/theme-provider"

interface Message {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

interface Chat {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
}

// Mock data for multiple chats with their messages
const mockChats: Chat[] = [
  {
    id: "chat-1",
    title: "React State Management",
    createdAt: new Date("2024-01-10T10:00:00"),
    updatedAt: new Date("2024-01-10T10:02:30"),
    messages: [
      {
        id: "1",
        content: "Hello! How can I help you today?",
        role: "assistant",
        timestamp: new Date("2024-01-10T10:00:00"),
      },
      {
        id: "2",
        content: "I need help with React state management. What are the best practices?",
        role: "user",
        timestamp: new Date("2024-01-10T10:01:00"),
      },
      {
        id: "3",
        content:
          "Great question! When working with React state management, here are some best practices:\n\n1. **Use the right tool for the job**:\n   - Local component state with useState for simple state\n   - useReducer for complex state logic\n   - Context API for sharing state between components\n   - External libraries like Redux, Zustand, or Jotai for global state\n\n2. **Keep state as local as possible**:\n   - Only lift state up when necessary\n   - Avoid prop drilling by using context\n\n3. **Separate UI state from business logic**:\n   - UI state: loading, error, form inputs\n   - Business logic: data, authentication, etc.\n\nWould you like me to elaborate on any of these points?",
        role: "assistant",
        timestamp: new Date("2024-01-10T10:01:30"),
      },
    ],
  },
  {
    id: "chat-2",
    title: "API Integration Help",
    createdAt: new Date("2024-01-09T15:30:00"),
    updatedAt: new Date("2024-01-09T16:45:00"),
    messages: [
      {
        id: "4",
        content: "Hi! I need help integrating APIs in my React app.",
        role: "user",
        timestamp: new Date("2024-01-09T15:30:00"),
      },
      {
        id: "5",
        content:
          "I'd be happy to help with API integration! Here are the common approaches:\n\n1. **Using fetch()** - Built-in browser API\n2. **Using axios** - Popular HTTP client library\n3. **Using React Query/TanStack Query** - For advanced data fetching\n4. **Using SWR** - For data fetching with caching\n\nWhat type of API are you working with?",
        role: "assistant",
        timestamp: new Date("2024-01-09T15:31:00"),
      },
      {
        id: "6",
        content: "I'm working with a REST API that returns JSON data. Should I use fetch or axios?",
        role: "user",
        timestamp: new Date("2024-01-09T15:32:00"),
      },
      {
        id: "7",
        content:
          "For a REST API returning JSON, both fetch and axios work well:\n\n**fetch()** - Good choice if:\n- You want to keep dependencies minimal\n- You're comfortable with promise-based syntax\n- You don't need advanced features\n\n**axios** - Better choice if:\n- You want automatic JSON parsing\n- You need request/response interceptors\n- You want better error handling\n- You need request timeout support\n\nFor most projects, I'd recommend starting with fetch() and moving to axios if you need more features.",
        role: "assistant",
        timestamp: new Date("2024-01-09T15:33:00"),
      },
    ],
  },
  {
    id: "chat-3",
    title: "CSS Grid Layout",
    createdAt: new Date("2024-01-08T09:15:00"),
    updatedAt: new Date("2024-01-08T10:30:00"),
    messages: [
      {
        id: "8",
        content: "Can you explain CSS Grid and when to use it?",
        role: "user",
        timestamp: new Date("2024-01-08T09:15:00"),
      },
      {
        id: "9",
        content:
          "CSS Grid is a powerful layout system! Here's when and how to use it:\n\n**When to use CSS Grid:**\n- Two-dimensional layouts (rows AND columns)\n- Complex layouts with overlapping elements\n- When you need precise control over placement\n- Card layouts, dashboards, magazine-style layouts\n\n**Basic Grid Setup:**\n```css\n.container {\n  display: grid;\n  grid-template-columns: 1fr 2fr 1fr;\n  grid-template-rows: auto 1fr auto;\n  gap: 1rem;\n}\n```\n\n**Grid vs Flexbox:**\n- Use Grid for 2D layouts\n- Use Flexbox for 1D layouts (single row/column)",
        role: "assistant",
        timestamp: new Date("2024-01-08T09:16:00"),
      },
    ],
  },
  {
    id: "chat-4",
    title: "JavaScript Promises",
    createdAt: new Date("2024-01-07T14:20:00"),
    updatedAt: new Date("2024-01-07T15:10:00"),
    messages: [
      {
        id: "10",
        content: "I'm confused about JavaScript Promises. Can you explain them?",
        role: "user",
        timestamp: new Date("2024-01-07T14:20:00"),
      },
      {
        id: "11",
        content:
          "Promises are a way to handle asynchronous operations in JavaScript!\n\n**What is a Promise?**\nA Promise represents a value that may be available now, in the future, or never.\n\n**Three States:**\n1. **Pending** - Initial state\n2. **Fulfilled** - Operation completed successfully\n3. **Rejected** - Operation failed\n\n**Basic Example:**\n```javascript\nconst myPromise = new Promise((resolve, reject) => {\n  setTimeout(() => {\n    resolve('Success!');\n  }, 1000);\n});\n\nmyPromise.then(result => {\n  console.log(result); // 'Success!'\n});\n```\n\n**Async/Await** is syntactic sugar for Promises:\n```javascript\nasync function fetchData() {\n  try {\n    const result = await myPromise;\n    console.log(result);\n  } catch (error) {\n    console.error(error);\n  }\n}\n```",
        role: "assistant",
        timestamp: new Date("2024-01-07T14:21:00"),
      },
    ],
  },
]

export default function ChatApp() {
  const [chats, setChats] = useState<Chat[]>(mockChats)
  const [activeChat, setActiveChat] = useState<string>("chat-1")
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const { setTheme, theme } = useTheme()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Get current chat and its messages
  const currentChat = chats.find((chat) => chat.id === activeChat)
  const messages = currentChat?.messages || []

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || !currentChat) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
      timestamp: new Date(),
    }

    // Add user message to current chat
    setChats((prevChats) =>
      prevChats.map((chat) =>
        chat.id === activeChat
          ? {
              ...chat,
              messages: [...chat.messages, userMessage],
              updatedAt: new Date(),
            }
          : chat,
      ),
    )

    setInput("")
    setIsLoading(true)

    // Simulate AI response delay
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content:
          "This is a mock response. In a real app, this would be generated by your AI service based on the conversation context.",
        role: "assistant",
        timestamp: new Date(),
      }

      // Add AI message to current chat
      setChats((prevChats) =>
        prevChats.map((chat) =>
          chat.id === activeChat
            ? {
                ...chat,
                messages: [...chat.messages, aiMessage],
                updatedAt: new Date(),
              }
            : chat,
        ),
      )
      setIsLoading(false)
    }, 1000)
  }

  const startNewChat = () => {
    const newChatId = `chat-${Date.now()}`
    const newChat: Chat = {
      id: newChatId,
      title: "New Conversation",
      messages: [
        {
          id: "welcome",
          content: "Hello! How can I help you today?",
          role: "assistant",
          timestamp: new Date(),
        },
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    setChats([newChat, ...chats])
    setActiveChat(newChatId)
  }

  const switchToChat = (chatId: string) => {
    setActiveChat(chatId)
    // Close sidebar on mobile after selecting a chat
    if (window.innerWidth < 768) {
      setSidebarOpen(false)
    }
  }

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }

  const getLastMessage = (chat: Chat) => {
    const lastMessage = chat.messages[chat.messages.length - 1]
    if (!lastMessage) return ""

    const preview = lastMessage.content.length > 50 ? lastMessage.content.substring(0, 50) + "..." : lastMessage.content

    return lastMessage.role === "user" ? `You: ${preview}` : preview
  }

  const formatChatTime = (date: Date) => {
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    } else if (diffInHours < 48) {
      return "Yesterday"
    } else {
      return date.toLocaleDateString([], { month: "short", day: "numeric" })
    }
  }

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-20 w-80 transform transition-transform duration-300 ease-in-out bg-card border-r border-border ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        } md:relative md:translate-x-0`}
      >
        <div className="flex h-full flex-col">
          <div className="p-4 border-b border-border">
            <Button
              onClick={startNewChat}
              className="w-full justify-start gap-2 bg-primary/10 hover:bg-primary/20 text-primary"
              variant="ghost"
            >
              <Plus className="h-4 w-4" />
              New chat
            </Button>
          </div>

          <ScrollArea className="flex-1 px-2 py-2">
            <div className="space-y-1">
              {chats
                .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
                .map((chat) => (
                  <Button
                    key={chat.id}
                    variant="ghost"
                    className={`w-full justify-start p-3 h-auto text-left hover:bg-accent/50 ${
                      activeChat === chat.id ? "bg-accent" : ""
                    }`}
                    onClick={() => switchToChat(chat.id)}
                  >
                    <div className="flex flex-col items-start gap-1 truncate w-full">
                      <div className="flex items-center justify-between w-full">
                        <span className="font-medium text-sm truncate flex-1">{chat.title}</span>
                        <span className="text-xs text-muted-foreground ml-2 flex-shrink-0">
                          {formatChatTime(chat.updatedAt)}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground truncate w-full">{getLastMessage(chat)}</span>
                    </div>
                  </Button>
                ))}
            </div>
          </ScrollArea>

          <div className="p-4 border-t border-border">
            <div className="flex items-center justify-between">
              <Button variant="ghost" size="icon" onClick={toggleTheme} className="rounded-full">
                {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>
              <span className="text-sm text-muted-foreground">ChatGPT Clone</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        {/* Header */}
        <header className="h-14 border-b border-border flex items-center px-4 shrink-0">
          <Button variant="ghost" size="icon" className="md:hidden mr-2" onClick={() => setSidebarOpen(!sidebarOpen)}>
            <Menu className="h-5 w-5" />
          </Button>
          <h1 className="font-semibold">{currentChat?.title || "Select a chat"}</h1>
          <div className="ml-auto text-sm text-muted-foreground">{messages.length} messages</div>
        </header>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 md:px-8 lg:px-16 xl:px-32">
          <div className="max-w-3xl mx-auto space-y-6">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === "user" ? "justify-end" : "justify-start"} group`}
              >
                <div
                  className={`flex gap-3 max-w-[90%] md:max-w-[80%] ${
                    message.role === "user" ? "flex-row-reverse" : ""
                  }`}
                >
                  <div className="flex-shrink-0 mt-1">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback
                        className={
                          message.role === "assistant"
                            ? "bg-primary/20 text-primary"
                            : "bg-secondary text-secondary-foreground"
                        }
                      >
                        {message.role === "assistant" ? <Bot className="h-4 w-4" /> : <User className="h-4 w-4" />}
                      </AvatarFallback>
                    </Avatar>
                  </div>

                  <div className="space-y-1">
                    <div
                      className={`whitespace-pre-wrap text-sm px-1 py-0.5 rounded ${
                        message.role === "user" ? "text-primary" : ""
                      }`}
                    >
                      {message.content}
                    </div>
                    <div className="text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity">
                      {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="flex gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-primary/20 text-primary">
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex items-center">
                    <div className="flex space-x-1.5 px-1 py-0.5">
                      <div className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-bounce"></div>
                      <div
                        className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-bounce"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-bounce"
                        style={{ animationDelay: "0.4s" }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input Area */}
        <div className="border-t border-border p-4 md:px-8 lg:px-16 xl:px-32">
          <div className="max-w-3xl mx-auto">
            <form onSubmit={handleSubmit} className="flex gap-2 items-center">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Message ChatGPT..."
                className="flex-1 bg-background border-border focus-visible:ring-primary"
                disabled={isLoading}
              />
              <Button
                type="submit"
                size="icon"
                disabled={isLoading || !input.trim()}
                className="bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                <Send className="h-4 w-4" />
              </Button>
            </form>
            <div className="text-xs text-center mt-2 text-muted-foreground">
              ChatGPT can make mistakes. Consider checking important information.
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
