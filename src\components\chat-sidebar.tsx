"use client"

import { <PERSON><PERSON> } from "~/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import { Badge } from "~/components/ui/badge"
import { MessageSquare, Plus, Settings, History } from "lucide-react"

// Mock data for chat history
const mockChatHistory = [
  {
    id: "1",
    title: "React State Management",
    lastMessage: "Great! For React state management...",
    timestamp: new Date("2024-01-10T10:02:30"),
    messageCount: 5,
  },
  {
    id: "2",
    title: "API Integration Help",
    lastMessage: "You can use fetch or axios...",
    timestamp: new Date("2024-01-09T15:30:00"),
    messageCount: 12,
  },
  {
    id: "3",
    title: "CSS Grid Layout",
    lastMessage: "CSS Grid is perfect for...",
    timestamp: new Date("2024-01-08T09:15:00"),
    messageCount: 8,
  },
]

interface ChatSidebarProps {
  className?: string
}

export default function ChatSidebar({ className }: ChatSidebarProps) {
  return (
    <Card className={`w-80 h-full ${className}`}>
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Chat History
          </CardTitle>
          <Button size="sm" variant="outline">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="p-4 border-b">
          <Button className="w-full justify-start gap-2" variant="default">
            <MessageSquare className="h-4 w-4" />
            New Chat
          </Button>
        </div>

        <div className="space-y-2 p-2">
          {mockChatHistory.map((chat) => (
            <Button key={chat.id} variant="ghost" className="w-full justify-start p-3 h-auto flex-col items-start">
              <div className="flex items-center justify-between w-full">
                <span className="font-medium text-sm truncate">{chat.title}</span>
                <Badge variant="secondary" className="text-xs">
                  {chat.messageCount}
                </Badge>
              </div>
              <span className="text-xs text-gray-500 truncate w-full text-left">{chat.lastMessage}</span>
              <span className="text-xs text-gray-400">{chat.timestamp.toLocaleDateString()}</span>
            </Button>
          ))}
        </div>

        <div className="absolute bottom-4 left-4 right-4">
          <Button variant="outline" className="w-full justify-start gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
